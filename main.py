#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频场景拆解工具
功能：根据用户设定的参数，自动将视频拆解成独立的场景并提取关键帧
作者：AI Assistant
版本：1.0.0
"""

import sys
import argparse
import cv2
import numpy as np
from pathlib import Path
from typing import List, Tuple
import time
from tqdm import tqdm

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp', '.webm'}




def parse_arguments() -> argparse.Namespace:
    """
    解析命令行参数

    Returns:
        解析后的参数对象
    """
    parser = argparse.ArgumentParser(
        description='AI视频场景拆解工具 - 自动将视频拆解成独立场景并提取关键帧',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  处理单个视频:
    python main.py input/my_video.mp4 output/ --sensitivity 0.2 --min_duration 1.5

  处理多个指定视频文件:
    python main.py input/video_A.mp4 input/video_C.mp4 output/ --sensitivity 0.2 --min_duration 1.5

  同时处理文件夹和单个视频文件:
    python main.py input/ input/video_X.mp4 output/ --sensitivity 0.2 --min_duration 1.5

参数说明:
  sensitivity: 场景检测灵敏度，推荐范围 0.05-0.30，值越小越灵敏
  min_duration: 场景之间的最短时间间隔，推荐范围 0.2-5.0 秒
        """
    )

    # 输入路径参数（可以是多个）
    parser.add_argument(
        'input_paths',
        nargs='+',
        help='输入路径，可以是一个或多个视频文件路径，或者文件夹路径'
    )

    # 输出路径参数
    parser.add_argument(
        'output_path',
        help='输出路径，所有输出文件夹的根目录'
    )

    # 灵敏度参数
    parser.add_argument(
        '--sensitivity',
        type=float,
        required=True,
        help='场景检测灵敏度 (0.05-0.30)，值越小越灵敏'
    )

    # 最小持续时间参数
    parser.add_argument(
        '--min_duration',
        type=float,
        required=True,
        help='场景之间的最短时间间隔，单位秒 (0.2-5.0)'
    )

    args = parser.parse_args()

    # 验证参数范围
    if not (0.01 <= args.sensitivity <= 1.0):
        parser.error("sensitivity 参数必须在 0.01-1.0 范围内")

    if not (0.1 <= args.min_duration <= 10.0):
        parser.error("min_duration 参数必须在 0.1-10.0 范围内")

    return args


def find_video_files(input_paths: List[str]) -> List[Path]:
    """
    从输入路径中查找所有视频文件

    Args:
        input_paths: 输入路径列表，可包含文件和文件夹

    Returns:
        找到的所有视频文件路径列表
    """
    video_files = []

    for input_path in input_paths:
        path = Path(input_path)

        if not path.exists():
            print(f"警告: 路径不存在: {input_path}")
            continue

        if path.is_file():
            # 检查是否为支持的视频格式
            if path.suffix.lower() in SUPPORTED_VIDEO_FORMATS:
                video_files.append(path)
            else:
                print(f"警告: 不支持的文件格式: {input_path}")
        elif path.is_dir():
            # 递归查找文件夹中的视频文件
            for video_file in path.rglob('*'):
                if video_file.is_file() and video_file.suffix.lower() in SUPPORTED_VIDEO_FORMATS:
                    video_files.append(video_file)

    return video_files


class VideoSceneDetector:
    """视频场景检测器类"""

    def __init__(self, sensitivity: float = 0.2, min_duration: float = 1.0):
        """
        初始化场景检测器

        Args:
            sensitivity: 场景检测灵敏度，值越小越灵敏 (0.05-0.30)
            min_duration: 场景之间的最短时间间隔，单位秒 (0.2-5.0)
        """
        self.sensitivity = sensitivity
        self.min_duration = min_duration

    def calculate_histogram(self, frame: np.ndarray) -> np.ndarray:
        """
        计算帧的颜色直方图

        Args:
            frame: 输入帧

        Returns:
            归一化的颜色直方图
        """
        # 转换为HSV颜色空间，更适合场景检测
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

        # 计算H、S、V三个通道的直方图
        hist_h = cv2.calcHist([hsv], [0], None, [50], [0, 180])
        hist_s = cv2.calcHist([hsv], [1], None, [60], [0, 256])
        hist_v = cv2.calcHist([hsv], [2], None, [60], [0, 256])

        # 合并直方图并归一化
        hist = np.concatenate([hist_h.flatten(), hist_s.flatten(), hist_v.flatten()])
        hist = cv2.normalize(hist, hist).flatten()

        return hist

    def compare_histograms(self, hist1: np.ndarray, hist2: np.ndarray) -> float:
        """
        比较两个直方图的相似度

        Args:
            hist1: 第一个直方图
            hist2: 第二个直方图

        Returns:
            相似度分数，值越大越相似
        """
        # 使用相关性方法比较直方图
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        return correlation

    def detect_scenes(self, video_path: Path) -> List[Tuple[int, float]]:
        """
        检测视频中的场景切换点

        Args:
            video_path: 视频文件路径

        Returns:
            场景切换点列表，每个元素为 (帧号, 时间戳)
        """
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")

        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        if fps <= 0:
            raise ValueError(f"无法获取视频帧率: {video_path}")

        scene_changes = [(0, 0.0)]  # 第一帧总是场景开始
        prev_hist = None
        frame_count = 0
        min_frame_interval = int(fps * self.min_duration)

        print(f"正在分析视频: {video_path.name}")
        print(f"总帧数: {total_frames}, 帧率: {fps:.2f} FPS")

        with tqdm(total=total_frames, desc="场景检测进度", unit="帧") as pbar:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 每隔几帧采样一次以提高效率
                if frame_count % max(1, int(fps / 5)) == 0:  # 每秒采样5次
                    # 调整帧大小以提高处理速度
                    height, width = frame.shape[:2]
                    if width > 640:
                        scale = 640 / width
                        new_width = 640
                        new_height = int(height * scale)
                        frame = cv2.resize(frame, (new_width, new_height))

                    current_hist = self.calculate_histogram(frame)

                    if prev_hist is not None:
                        similarity = self.compare_histograms(prev_hist, current_hist)

                        # 如果相似度低于阈值，且距离上一个场景切换点足够远
                        if (similarity < (1.0 - self.sensitivity) and
                            frame_count - scene_changes[-1][0] >= min_frame_interval):

                            timestamp = frame_count / fps
                            scene_changes.append((frame_count, timestamp))
                            print(f"检测到场景切换: 第{frame_count}帧, 时间{timestamp:.2f}秒, 相似度{similarity:.3f}")

                    prev_hist = current_hist

                frame_count += 1
                pbar.update(1)

        cap.release()

        # 添加视频结束点
        final_timestamp = (total_frames - 1) / fps
        if scene_changes[-1][0] != total_frames - 1:
            scene_changes.append((total_frames - 1, final_timestamp))

        print(f"场景检测完成，共检测到 {len(scene_changes)} 个场景切换点")
        return scene_changes

    def extract_key_frames(self, video_path: Path, scene_changes: List[Tuple[int, float]],
                          output_dir: Path) -> None:
        """
        从检测到的场景中提取关键帧

        Args:
            video_path: 视频文件路径
            scene_changes: 场景切换点列表
            output_dir: 输出目录
        """
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")

        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"正在提取关键帧到: {output_dir}")

        saved_count = 0
        for i in range(len(scene_changes) - 1):
            start_frame, start_time = scene_changes[i]
            end_frame, end_time = scene_changes[i + 1]

            # 选择场景中间的帧作为关键帧
            key_frame_num = start_frame + (end_frame - start_frame) // 2

            # 跳转到关键帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, key_frame_num)
            ret, frame = cap.read()

            if ret:
                # 保存关键帧
                saved_count += 1
                output_filename = output_dir / f"{saved_count}.jpg"

                # 使用高质量保存
                cv2.imwrite(str(output_filename), frame,
                           [cv2.IMWRITE_JPEG_QUALITY, 95])

                scene_duration = end_time - start_time
                print(f"保存场景 {saved_count}: 帧{key_frame_num} "
                      f"(时间{key_frame_num/cap.get(cv2.CAP_PROP_FPS):.2f}s, "
                      f"场景时长{scene_duration:.2f}s)")

        cap.release()
        print(f"关键帧提取完成，共保存 {saved_count} 张图片")


def process_single_video(video_path: Path, output_root: Path, detector: VideoSceneDetector) -> bool:
    """
    处理单个视频文件

    Args:
        video_path: 视频文件路径
        output_root: 输出根目录
        detector: 场景检测器实例

    Returns:
        处理是否成功
    """
    try:
        print(f"\n{'='*60}")
        print(f"开始处理视频: {video_path}")
        print(f"{'='*60}")

        # 创建以视频文件名命名的输出目录
        video_name = video_path.stem  # 不包含扩展名的文件名
        output_dir = output_root / video_name

        # 检测场景切换点
        scene_changes = detector.detect_scenes(video_path)

        if len(scene_changes) < 2:
            print(f"警告: 视频 {video_path.name} 中未检测到场景切换")
            return False

        # 提取关键帧
        detector.extract_key_frames(video_path, scene_changes, output_dir)

        print(f"视频 {video_path.name} 处理完成")
        return True

    except Exception as e:
        print(f"处理视频 {video_path} 时发生错误: {str(e)}")
        return False


def validate_environment() -> bool:
    """
    验证运行环境和依赖

    Returns:
        环境是否满足要求
    """
    try:
        # 检查OpenCV版本
        cv_version = cv2.__version__
        print(f"OpenCV 版本: {cv_version}")

        # 检查numpy版本
        np_version = np.__version__
        print(f"NumPy 版本: {np_version}")

        return True

    except Exception as e:
        print(f"环境验证失败: {str(e)}")
        print("请确保已安装必要的依赖包:")
        print("pip install opencv-python numpy tqdm")
        return False


def print_summary(total_videos: int, successful: int, failed: int, start_time: float) -> None:
    """
    打印处理结果摘要

    Args:
        total_videos: 总视频数
        successful: 成功处理数
        failed: 失败处理数
        start_time: 开始时间
    """
    end_time = time.time()
    duration = end_time - start_time

    print(f"\n{'='*60}")
    print("处理结果摘要")
    print(f"{'='*60}")
    print(f"总视频数: {total_videos}")
    print(f"成功处理: {successful}")
    print(f"处理失败: {failed}")
    print(f"总耗时: {duration:.2f} 秒")

    if successful > 0:
        print(f"平均每个视频耗时: {duration/successful:.2f} 秒")

    if failed > 0:
        print(f"失败率: {failed/total_videos*100:.1f}%")
    else:
        print("所有视频处理成功！")


def main():
    """主函数"""
    try:
        # 验证环境
        if not validate_environment():
            sys.exit(1)

        # 解析命令行参数
        args = parse_arguments()

        # 查找所有视频文件
        print("正在搜索视频文件...")
        video_files = find_video_files(args.input_paths)

        if not video_files:
            print("错误: 未找到任何支持的视频文件")
            print(f"支持的格式: {', '.join(SUPPORTED_VIDEO_FORMATS)}")
            sys.exit(1)

        print(f"找到 {len(video_files)} 个视频文件")
        for video_file in video_files:
            print(f"  - {video_file}")

        # 创建输出根目录
        output_root = Path(args.output_path)
        output_root.mkdir(parents=True, exist_ok=True)

        # 创建场景检测器
        detector = VideoSceneDetector(
            sensitivity=args.sensitivity,
            min_duration=args.min_duration
        )

        print(f"\n场景检测参数:")
        print(f"  灵敏度: {args.sensitivity}")
        print(f"  最小持续时间: {args.min_duration} 秒")

        # 处理所有视频
        start_time = time.time()
        successful = 0
        failed = 0

        for video_file in video_files:
            if process_single_video(video_file, output_root, detector):
                successful += 1
            else:
                failed += 1

        # 打印处理结果摘要
        print_summary(len(video_files), successful, failed, start_time)

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
