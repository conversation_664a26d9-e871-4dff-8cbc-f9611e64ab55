# AI视频场景拆解工具

一款功能强大且易于使用的 AI 视频场景拆解工具，能够根据用户设定的参数，自动将视频拆解成独立的场景并提取关键帧。

## 功能特点

- 🎯 **智能场景检测**: 基于直方图比较算法，准确识别视频中的场景切换点
- 🚀 **批量处理**: 支持单个视频、多个视频文件或整个文件夹的批量处理
- ⚡ **高效处理**: 优化的算法确保快速处理大量视频文件
- 🎨 **高质量输出**: 提取的关键帧保持原始画质
- 📊 **进度显示**: 实时显示处理进度和详细统计信息
- 🛠️ **灵活配置**: 可调节的灵敏度和时间间隔参数

## 安装依赖

在使用工具之前，请确保安装以下依赖包：

```bash
pip install opencv-python numpy tqdm
```

## 使用方法

### 基本语法

```bash
python main.py <输入路径> [更多输入路径...] <导出路径> --sensitivity <值> --min_duration <值>
```

### 参数说明

- `<输入路径>`: 必填，可以是一个或多个视频文件路径，或者文件夹路径
- `<导出路径>`: 必填，所有输出文件夹的根目录
- `--sensitivity`: 必填，浮点数，推荐范围 0.05-0.30，值越小场景检测越灵敏
- `--min_duration`: 必填，浮点数，推荐范围 0.2-5.0 秒，设定场景之间的最短时间间隔

### 使用示例

#### 1. 处理单个视频文件

```bash
python main.py input/my_video.mp4 output/ --sensitivity 0.2 --min_duration 1.5
```

#### 2. 处理多个指定视频文件

```bash
python main.py input/video_A.mp4 input/video_C.mp4 output/ --sensitivity 0.2 --min_duration 1.5
```

#### 3. 处理整个文件夹

```bash
python main.py input/ output/ --sensitivity 0.15 --min_duration 2.0
```

#### 4. 混合处理（文件夹 + 单个文件）

```bash
python main.py input/ input/special_video.mp4 output/ --sensitivity 0.2 --min_duration 1.5
```

## 输出结构

工具会为每个视频文件创建一个以视频文件名（不含扩展名）命名的子文件夹，所有提取的关键帧按 `1.jpg`, `2.jpg`, `3.jpg`... 的顺序保存。

### 示例输出结构

假设执行命令：
```bash
python main.py input/video_A.mp4 input/videos_folder/ output/ --sensitivity 0.2 --min_duration 1.5
```

其中 `videos_folder/` 包含 `video_B.mp4` 和 `video_C.mp4`，最终输出结构为：

```
output/
├── video_A/
│   ├── 1.jpg
│   ├── 2.jpg
│   └── 3.jpg
└── videos_folder/
    ├── video_B/
    │   ├── 1.jpg
    │   └── 2.jpg
    └── video_C/
        ├── 1.jpg
        ├── 2.jpg
        └── 3.jpg
```

## 支持的视频格式

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- FLV (.flv)
- WMV (.wmv)
- M4V (.m4v)
- 3GP (.3gp)
- WebM (.webm)

## 参数调优建议

### sensitivity (灵敏度)

- **0.05-0.10**: 极高灵敏度，适合检测细微的场景变化
- **0.10-0.20**: 高灵敏度，适合大多数视频内容
- **0.20-0.30**: 中等灵敏度，适合场景变化明显的视频
- **0.30+**: 低灵敏度，只检测明显的场景切换

### min_duration (最小持续时间)

- **0.2-1.0秒**: 适合快节奏视频，如音乐视频、广告
- **1.0-3.0秒**: 适合一般视频内容
- **3.0-5.0秒**: 适合慢节奏视频，如纪录片、讲座

## 技术实现

- **场景检测算法**: 基于HSV颜色空间的直方图比较
- **视频处理**: 使用OpenCV进行高效的视频读取和帧处理
- **性能优化**: 智能采样和帧大小调整，提高处理速度
- **错误处理**: 完善的异常捕获和用户友好的错误提示

## 注意事项

1. 确保有足够的磁盘空间存储提取的关键帧
2. 处理大量视频时建议分批进行
3. 如果视频文件损坏或格式不支持，工具会跳过并继续处理其他文件
4. 建议在处理前先用小批量视频测试参数设置

## 故障排除

### 常见问题

1. **"无法打开视频文件"**: 检查视频文件是否损坏或格式是否支持
2. **"未检测到场景切换"**: 尝试降低sensitivity值或减少min_duration值
3. **处理速度慢**: 可以适当增加sensitivity值或减少视频分辨率

### 获取帮助

运行以下命令查看详细帮助信息：

```bash
python main.py --help
```
