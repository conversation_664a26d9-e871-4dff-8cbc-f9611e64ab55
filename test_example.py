#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试示例脚本
用于验证视频场景拆解工具的基本功能
"""

import subprocess
import sys
from pathlib import Path

def test_help():
    """测试帮助信息显示"""
    print("测试帮助信息...")
    try:
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ 帮助信息显示正常")
            return True
        else:
            print("✗ 帮助信息显示失败")
            return False
    except Exception as e:
        print(f"✗ 测试帮助信息时出错: {e}")
        return False

def test_parameter_validation():
    """测试参数验证"""
    print("\n测试参数验证...")
    
    # 测试缺少必需参数
    test_cases = [
        # 缺少sensitivity参数
        ["main.py", "test_input", "test_output", "--min_duration", "1.0"],
        # 缺少min_duration参数
        ["main.py", "test_input", "test_output", "--sensitivity", "0.2"],
        # sensitivity参数超出范围
        ["main.py", "test_input", "test_output", "--sensitivity", "2.0", "--min_duration", "1.0"],
        # min_duration参数超出范围
        ["main.py", "test_input", "test_output", "--sensitivity", "0.2", "--min_duration", "20.0"],
    ]
    
    passed = 0
    for i, cmd in enumerate(test_cases, 1):
        try:
            result = subprocess.run([sys.executable] + cmd, 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"✓ 测试用例 {i}: 参数验证正常")
                passed += 1
            else:
                print(f"✗ 测试用例 {i}: 参数验证失败")
        except Exception as e:
            print(f"✗ 测试用例 {i} 出错: {e}")
    
    print(f"参数验证测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)

def test_environment():
    """测试运行环境"""
    print("\n测试运行环境...")
    try:
        import cv2
        import numpy as np
        import tqdm
        
        print(f"✓ OpenCV 版本: {cv2.__version__}")
        print(f"✓ NumPy 版本: {np.__version__}")
        print(f"✓ tqdm 版本: {tqdm.__version__}")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def create_sample_structure():
    """创建示例目录结构"""
    print("\n创建示例目录结构...")
    
    # 创建示例输入和输出目录
    input_dir = Path("sample_input")
    output_dir = Path("sample_output")
    
    input_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)
    
    print(f"✓ 创建目录: {input_dir}")
    print(f"✓ 创建目录: {output_dir}")
    
    # 创建示例命令文件
    sample_commands = Path("sample_commands.txt")
    with open(sample_commands, 'w', encoding='utf-8') as f:
        f.write("""# 示例命令（需要先准备视频文件）

# 处理单个视频文件
python main.py sample_input/video.mp4 sample_output/ --sensitivity 0.2 --min_duration 1.5

# 处理整个文件夹
python main.py sample_input/ sample_output/ --sensitivity 0.15 --min_duration 2.0

# 处理多个文件
python main.py sample_input/video1.mp4 sample_input/video2.mp4 sample_output/ --sensitivity 0.2 --min_duration 1.0

# 注意：请将您的视频文件放入 sample_input/ 目录中，然后运行上述命令
""")
    
    print(f"✓ 创建示例命令文件: {sample_commands}")
    return True

def main():
    """主测试函数"""
    print("AI视频场景拆解工具 - 功能测试")
    print("=" * 50)
    
    # 测试环境
    if not test_environment():
        print("\n❌ 环境测试失败，请先安装依赖包")
        return False
    
    # 测试帮助信息
    if not test_help():
        print("\n❌ 帮助信息测试失败")
        return False
    
    # 测试参数验证
    if not test_parameter_validation():
        print("\n❌ 参数验证测试失败")
        return False
    
    # 创建示例结构
    create_sample_structure()
    
    print("\n" + "=" * 50)
    print("✅ 所有基础测试通过！")
    print("\n下一步:")
    print("1. 将视频文件放入 sample_input/ 目录")
    print("2. 参考 sample_commands.txt 中的示例命令")
    print("3. 运行实际的视频处理测试")
    
    return True

if __name__ == "__main__":
    main()
