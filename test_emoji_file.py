#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试处理包含emoji的文件
"""

import subprocess
import sys
from pathlib import Path

def test_emoji_file():
    """测试处理包含emoji的文件"""
    
    # 直接在代码中指定文件路径，避免命令行传递问题
    input_dir = Path(r"E:\inbox\inbox")
    output_dir = Path(r"E:\inbox\inbox")
    
    # 查找包含emoji的文件
    video_files = []
    for file in input_dir.glob("*.mp4"):
        print(f"找到文件: {file.name}")
        video_files.append(file)
    
    if not video_files:
        print("未找到MP4文件")
        return
    
    # 选择包含特殊字符的文件
    target_file = None
    for file in video_files:
        if "🐚" in file.name or "😿" in file.name:
            target_file = file
            break
    
    if not target_file:
        # 如果没找到emoji文件，使用第一个文件
        target_file = video_files[0]
    
    print(f"将处理文件: {target_file}")
    
    # 构建命令
    cmd = [
        sys.executable, "main.py",
        str(target_file),
        str(output_dir),
        "--sensitivity", "0.1",
        "--min_duration", "1.5"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
    except Exception as e:
        print(f"执行出错: {e}")

if __name__ == "__main__":
    test_emoji_file()
